import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Info } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  FormData,
  techs,
  energySources,
  emissions,
  materials,
  units,
  EnergyInput,
  EmissionInput,
  MaterialInput,
  TechnologyResponse,
  MaterialResponse,
  EnergyResponse,
  EmissionResponse
} from "./types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { filterValidNodes, getNodeLabel } from "@/utils/dropdownUtils";

interface InputsStepProps {
  formData: FormData;
  updateField: (field: string, value: string | any[]) => void;
  updateFormField: (section: string, field: string, value: string) => void;
  errors: Record<string, string>;
  usingManualEntry: boolean;
  energyInputAutoFillLabel: string;
  matInputAutoFillLabel: string;
  technologyAutoFillLabel: string;
  availableTechnologies: string[];
  availableNodes?: any[];
  technologies: string[];
  activeTechnology: string;
  setActiveTechnology: (tech: string) => void;
  updateTechnologyName: (oldTechName: string, newTechName: string) => void;
  readOnly?: boolean;
  apiTechnologies?: TechnologyResponse[];
  apiMaterials?: MaterialResponse[];
  apiEnergies?: EnergyResponse[];
  apiEmissions?: EmissionResponse[];
  isLoadingApiData?: boolean;
  onCreateTechnology?: (name: string, description?: string) => Promise<TechnologyResponse | null>;
}

export const InputsStep: React.FC<InputsStepProps> = ({
  formData,
  updateField,
  updateFormField,
  errors,
  usingManualEntry,
  energyInputAutoFillLabel,
  matInputAutoFillLabel,
  technologyAutoFillLabel,
  availableTechnologies = [],
  availableNodes = [],
  technologies,
  activeTechnology,
  setActiveTechnology,
  updateTechnologyName,
  readOnly = true,
  apiTechnologies = [],
  apiMaterials = [],
  apiEnergies = [],
  apiEmissions = [],
  isLoadingApiData = false,
  onCreateTechnology
}) => {

  // State for custom technology handling
  const [showCustomTechInput, setShowCustomTechInput] = useState(false);
  const [customTechnology, setCustomTechnology] = useState("");

  // Initialize state for dynamic inputs
  const [energyInputs, setEnergyInputs] = useState<EnergyInput[]>([]);
  const [emissionInputs, setEmissionInputs] = useState<EmissionInput[]>([]);
  const [materialInputs, setMaterialInputs] = useState<MaterialInput[]>([]);

  // Flag to track if we're loading data (to prevent sync during load)
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Update local state when formData changes (technology switching)
  useEffect(() => {
    setIsLoadingData(true);

    // Update energy inputs from formData
    if (formData.energyInputs && formData.energyInputs.length > 0) {
      setEnergyInputs(formData.energyInputs);
    } else {
      // Initialize with one empty entry if no data
      setEnergyInputs([{
        id: `energy-${Date.now()}`,
        source: formData.energyInput?.source || "",
        unit: formData.energyInput?.unit || "GJ",
        cost: formData.energyInput?.cost || "",
        sec: formData.energyInput?.sec || "",
        sourceActivity: "Nil",
        technology: "Nil"
      }]);
    }

    // Update emission inputs from formData
    if (formData.emissions && formData.emissions.length > 0) {
      setEmissionInputs(formData.emissions);
    } else {
      // Initialize with one empty entry if no data
      setEmissionInputs([{
        id: `emission-${Date.now()}`,
        source: formData.emission?.source || "",
        factor: formData.emission?.ef || "",
        unit: formData.emission?.unit || "kg"
      }]);
    }

    // Update material inputs from formData
    if (formData.materialInputs && formData.materialInputs.length > 0) {
      setMaterialInputs(formData.materialInputs);
    } else {
      // Initialize with one empty entry if no data
      setMaterialInputs([{
        id: `material-${Date.now()}`,
        material: formData.matInput?.material || "",
        unit: formData.matInput?.unit || "Tonnes",
        cost: formData.matInput?.cost || "",
        smc: formData.matInput?.smc || "",
        sourceActivity: "Nil",
        technology: "Nil"
      }]);
    }

    // Set loading to false after a brief delay to allow state to settle
    setTimeout(() => setIsLoadingData(false), 100);
  }, [formData, activeTechnology]);

  // // Sync local state changes back to parent technology form data
  // // Only sync when user is making changes (not when loading data)
  // useEffect(() => {
  //   if (!isLoadingData && energyInputs.length > 0) {
  //     updateField('energyInputs', energyInputs);
  //   }
  // }, [energyInputs, isLoadingData]);

  // useEffect(() => {
  //   if (!isLoadingData && emissionInputs.length > 0) {
  //     updateField('emissions', emissionInputs);
  //   }
  // }, [emissionInputs, isLoadingData]);

  // useEffect(() => {
  //   if (!isLoadingData && materialInputs.length > 0) {
  //     updateField('materialInputs', materialInputs);
  //   }
  // }, [materialInputs, isLoadingData]);

  // Filter available nodes and get all technologies
  const validNodes = filterValidNodes(availableNodes);

  // Combine API technologies with fallback technologies
  const allTechnologies = apiTechnologies.length > 0
    ? apiTechnologies.map(tech => tech.name).filter(name => name && name.trim() !== "")
    : [...new Set([...availableTechnologies, ...techs])].filter(tech => tech && tech.trim() !== "");

  // Get materials, energies, and emissions from API or fallback
  const availableMaterials = apiMaterials.length > 0
    ? apiMaterials.map(material => material.name).filter(name => name && name.trim() !== "")
    : materials.filter(material => material && material.trim() !== "");

  const availableEnergies = apiEnergies.length > 0
    ? apiEnergies.map(energy => energy.name).filter(name => name && name.trim() !== "")
    : energySources.filter(energy => energy && energy.trim() !== "");

  const availableEmissions = apiEmissions.length > 0
    ? apiEmissions.map(emission => emission.name).filter(name => name && name.trim() !== "")
    : emissions.filter(emission => emission && emission.trim() !== "");

  // Technology handling functions
  const handleTechnologyChange = async (value: string) => {
    if (value === "add_custom") {
      setShowCustomTechInput(true);
    } else if (value) {
      updateField('technology', value);
      setShowCustomTechInput(false);
      // Update the technology name in the tab header
      updateTechnologyName(activeTechnology, value);
    }
  };

  const handleCustomTechnologySubmit = async () => {
    if (customTechnology.trim()) {
      const newTech = customTechnology.trim();

      // Try to create technology via API if available
      if (onCreateTechnology) {
        const createdTech = await onCreateTechnology(newTech);
        if (createdTech) {
          updateField('technology', createdTech.name);
          updateTechnologyName(activeTechnology, createdTech.name);
        } else {
          // Fallback to local creation if API fails
          updateField('technology', newTech);
          updateTechnologyName(activeTechnology, newTech);
        }
      } else {
        // Fallback to local creation if no API handler
        updateField('technology', newTech);
        updateTechnologyName(activeTechnology, newTech);
      }

      setCustomTechnology("");
      setShowCustomTechInput(false);
    }
  };

  // Handler for updating energy input
  const updateEnergyInput = (id: string, field: string, value: string) => {
    setEnergyInputs(prevInputs =>
      prevInputs.map(input => {
        if (input.id === id) {
          const updatedInput = { ...input, [field]: value };
          // If source activity is set to "Nil", automatically set technology to "Nil"
          if (field === 'sourceActivity' && value === 'Nil') {
            updatedInput.technology = 'Nil';
          }
          // If source activity changes from "Nil" to something else, reset technology if it was "Nil"
          else if (field === 'sourceActivity' && value !== 'Nil' && input.technology === 'Nil') {
            updatedInput.technology = '';
          }
          return updatedInput;
        }
        return input;
      })
    );
  };

  // Handler for updating emission input
  const updateEmissionInput = (id: string, field: string, value: string) => {
    setEmissionInputs(prevInputs => 
      prevInputs.map(input => 
        input.id === id ? { ...input, [field]: value } : input
      )
    );
  };

  // Handler for updating material input
  const updateMaterialInput = (id: string, field: string, value: string) => {
    setMaterialInputs(prevInputs =>
      prevInputs.map(input => {
        if (input.id === id) {
          const updatedInput = { ...input, [field]: value };
          // If source activity is set to "Nil", automatically set technology to "Nil"
          if (field === 'sourceActivity' && value === 'Nil') {
            updatedInput.technology = 'Nil';
          }
          // If source activity changes from "Nil" to something else, reset technology if it was "Nil"
          else if (field === 'sourceActivity' && value !== 'Nil' && input.technology === 'Nil') {
            updatedInput.technology = '';
          }
          return updatedInput;
        }
        return input;
      })
    );
  };

  return (
    <>
      <div className="font-semibold mb-2 flex items-center justify-between">
        <span>Inputs</span>
      </div>

      <Tabs value={activeTechnology} onValueChange={setActiveTechnology} className="w-full">
        {/* <TabsList className="mb-2 w-full overflow-x-auto flex whitespace-nowrap">
          {technologies.map((tech) => (
            <TabsTrigger
              key={tech}
              value={tech}
              className="min-w-[100px] flex-shrink-0"
            >
              {tech}
            </TabsTrigger>
          ))}
        </TabsList> */}

        {technologies.map((tech) => (
          <TabsContent key={tech} value={tech}>
            {renderTechnologyContent(tech)}
          </TabsContent>
        ))}
      </Tabs>
    </>
  );

  function renderTechnologyContent(technology: string) {
    return (
      <>
        {/* Technology Selection Field */}
        <div className="border-2 border-orange-300 rounded-md mb-3 p-3">
          <div className="font-semibold mb-1">Technology</div>
          <div className="flex flex-col gap-2">
            {!showCustomTechInput ? (
              <>
                {/* Technology and Life of Technology section - aligned */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Technology dropdown */}
                  <div className="flex flex-col justify-end">
                    <label className="block text-sm font-medium mb-1 text-gray-600">Select Technology</label>
                    {readOnly ? (
                      <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300 h-10 flex items-center">
                        {activeTechnology || "Select technology"}
                      </div>
                    ) : (
                      <Select
                        value={activeTechnology || undefined}
                        onValueChange={handleTechnologyChange}
                      >
                        <SelectTrigger className="w-full h-10">
                          <SelectValue placeholder="Select technology" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingApiData ? (
                            <SelectItem value="loading" disabled>Loading technologies...</SelectItem>
                          ) : (
                            <>
                              {allTechnologies.map(tech => (
                                <SelectItem key={tech} value={tech}>{tech}</SelectItem>
                              ))}
                              <SelectItem value="add_custom">+ Add new technology</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  </div>

                  {/* Life of Technology section */}
                  <div className="flex flex-col">
                    <div className="mb-2">
                      <span className="text-sm font-medium text-gray-600">Life of Technology</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {/* Start Year dropdown */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-medium mb-1 text-gray-600">Start Year</label>
                        {readOnly ? (
                          <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300 h-10 flex items-center">
                            {formData.startYear || "2000"}
                          </div>
                        ) : (
                          <Select
                            value={formData.startYear || "2000"}
                            onValueChange={(value) => updateField('startYear', value)}
                          >
                            <SelectTrigger className="w-full h-10">
                              <SelectValue placeholder="Start Year" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 76 }, (_, i) => 2000 + i).map(year => (
                                <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>

                      {/* End Year dropdown */}
                      <div className="flex flex-col">
                        <label className="block text-sm font-medium mb-1 text-gray-600">End Year</label>
                        {readOnly ? (
                          <div className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600 cursor-not-allowed opacity-80 border-gray-300 h-10 flex items-center">
                            {formData.endYear || "2075"}
                          </div>
                        ) : (
                          <Select
                            value={formData.endYear || "2075"}
                            onValueChange={(value) => updateField('endYear', value)}
                          >
                            <SelectTrigger className="w-full h-10">
                              <SelectValue placeholder="End Year" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({ length: 76 }, (_, i) => 2000 + i).map(year => (
                                <SelectItem key={year} value={year.toString()}>{year}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Technology Emission Section */}
                <div className="mt-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="font-semibold">Emission</div>
                    <div className="group relative">
                      <button
                        type="button"
                        className="w-4 h-4 rounded-full bg-gray-300 text-white text-xs flex items-center justify-center hover:bg-gray-400 transition-colors"
                        title="Total emission produced by the technology"
                      >
                        i
                      </button>
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <Input
                      className="text-sm"
                      value={formData.technologyEmission || ""}
                      onChange={(e) => updateField('technologyEmission', e.target.value)}
                      disabled={readOnly}
                      placeholder="Enter emission value"
                    />
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 min-w-0">
                <label className="block text-sm font-medium mb-1">Enter new technology name</label>
                <div className="flex gap-2">
                  <Input
                    value={customTechnology}
                    onChange={(e) => setCustomTechnology(e.target.value)}
                    placeholder="Type new technology name"
                    className="flex-1"
                    disabled={readOnly}
                  />
                  <Button
                    onClick={handleCustomTechnologySubmit}
                    type="button"
                    disabled={customTechnology.trim() === "" || readOnly}
                  >
                    Add
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowCustomTechInput(false)}
                    type="button"
                    disabled={readOnly}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Combined Energy Input and Emissions Section */}
      <div className="border-2 border-blue-300 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Energy Input</div>
        </div>

        {/* Render dynamic energy inputs */}
        {energyInputs.map((energyInput, index) => (
          <div 
            key={energyInput.id} 
            className={`${index > 0 ? 'mt-3 pt-3 border-t border-gray-200' : ''}`}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Name</label>
                {index === 0 && energyInputAutoFillLabel ? (
                  <div className="border rounded px-2 py-2 bg-gray-50 text-sm">
                    {energyInputAutoFillLabel}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2 text-sm"
                    value={energyInput.source}
                    onChange={(e) => updateEnergyInput(energyInput.id, 'source', e.target.value)}
                    disabled={isLoadingApiData || readOnly}
                  >
                    <option value="">{isLoadingApiData ? "Loading energies..." : "Select"}</option>
                    {availableEnergies.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Unit</label>
                <select
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={energyInput.unit}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'unit', e.target.value)}
                  disabled={readOnly}
                >
                  {units.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>
              
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Cost/Unit</label>
                <Input
                  className="text-sm"
                  value={energyInput.cost}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'cost', e.target.value)}
                  disabled={readOnly}
                />
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">SEC</label>
                <Input
                  className="text-sm"
                  value={energyInput.sec}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'sec', e.target.value)}
                  disabled={readOnly}
                />
              </div>
            </div>

            {/* Lower and Upper Bounds */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Lower Bound (%)</label>
                  <div className="group relative">
                    <button
                      type="button"
                      className="w-4 h-4 rounded-full bg-gray-300 text-white text-xs flex items-center justify-center hover:bg-gray-400 transition-colors"
                      title="Min percentage of this energy allowed in the current energy mix"
                    >
                      i
                    </button>
                  </div>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={energyInput.lowerBound || ""}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'lowerBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="0"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Upper Bound (%)</label>
                  <div className="group relative">
                    <button
                      type="button"
                      className="w-4 h-4 rounded-full bg-gray-300 text-white text-xs flex items-center justify-center hover:bg-gray-400 transition-colors"
                      title="Max percentage of this energy allowed in the current energy mix"
                    >
                      i
                    </button>
                  </div>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={energyInput.upperBound || ""}
                  onChange={(e) => updateEnergyInput(energyInput.id, 'upperBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="100"
                />
              </div>
            </div>
          </div>
        ))}

        {/* Emissions Section - now part of the same block */}
        <div className="flex items-center justify-between mt-4 mb-1">
          <div className="font-semibold">Emissions</div>
        </div>

        {/* Render dynamic emission inputs */}
        {emissionInputs.map((emission, index) => (
          <div 
            key={emission.id} 
            className={`${index > 0 ? 'mt-3 pt-3 border-t border-gray-200' : ''}`}
          >
            <div className="flex flex-col md:flex-row gap-2">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Name</label>
                <select
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={emission.source}
                  onChange={(e) => updateEmissionInput(emission.id, 'source', e.target.value)}
                  disabled={isLoadingApiData || readOnly}
                >
                  <option value="">{isLoadingApiData ? "Loading emissions..." : "Select"}</option>
                  {availableEmissions.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-1 mb-1">
                  <label className="text-sm font-medium">Emission Factor</label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info className="h-3 w-3 text-gray-400 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Emissions per unit of energy</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  className="text-sm"
                  value={emission.factor}
                  onChange={(e) => updateEmissionInput(emission.id, 'factor', e.target.value)}
                  disabled={readOnly}
                />
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Unit</label>
                <select
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={emission.unit}
                  onChange={(e) => updateEmissionInput(emission.id, 'unit', e.target.value)}
                  disabled={readOnly}
                >
                  {units.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Material Input Section */}
      <div className="border-2 border-gray-200 rounded-md mb-3 p-3">
        <div className="flex items-center justify-between mb-1">
          <div className="font-semibold">Material Input</div>
        </div>
        
        {/* Render dynamic material inputs */}
        {materialInputs.map((materialInput, index) => (
          <div 
            key={materialInput.id} 
            className={`${index > 0 ? 'mt-3 pt-3 border-t border-gray-200' : ''}`}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3">
              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Name</label>
                {index === 0 && matInputAutoFillLabel ? (
                  <div className="border rounded px-2 py-2 bg-gray-50 text-sm">
                    {matInputAutoFillLabel}
                  </div>
                ) : (
                  <select
                    className="w-full border rounded px-2 py-2 text-sm"
                    value={materialInput.material}
                    onChange={(e) => updateMaterialInput(materialInput.id, 'material', e.target.value)}
                    disabled={isLoadingApiData || readOnly}
                  >
                    <option value="">{isLoadingApiData ? "Loading materials..." : "Select"}</option>
                    {availableMaterials.map(v => <option key={v} value={v}>{v}</option>)}
                  </select>
                )}
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Unit</label>
                <select
                  className="w-full border rounded px-2 py-2 text-sm"
                  value={materialInput.unit}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'unit', e.target.value)}
                  disabled={readOnly}
                >
                  {units.map(v => <option key={v} value={v}>{v}</option>)}
                </select>
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">Cost/Unit</label>
                <Input
                  className="text-sm"
                  value={materialInput.cost}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'cost', e.target.value)}
                  disabled={readOnly}
                />
              </div>

              <div className="flex-1 min-w-0">
                <label className="text-sm font-medium">SMC</label>
                <Input
                  className="text-sm"
                  value={materialInput.smc}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'smc', e.target.value)}
                  disabled={readOnly}
                />
              </div>
            </div>

            {/* Lower and Upper Bounds */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Lower Bound (%)</label>
                  <div className="group relative">
                    <button
                      type="button"
                      className="w-4 h-4 rounded-full bg-gray-300 text-white text-xs flex items-center justify-center hover:bg-gray-400 transition-colors"
                      title="Min percentage of this material allowed in the current energy mix"
                    >
                      i
                    </button>
                  </div>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={materialInput.lowerBound || ""}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'lowerBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="0"
                />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium">Upper Bound (%)</label>
                  <div className="group relative">
                    <button
                      type="button"
                      className="w-4 h-4 rounded-full bg-gray-300 text-white text-xs flex items-center justify-center hover:bg-gray-400 transition-colors"
                      title="Max percentage of this material allowed in the current energy mix"
                    >
                      i
                    </button>
                  </div>
                </div>
                <Input
                  className="text-sm"
                  type="number"
                  min="0"
                  max="100"
                  value={materialInput.upperBound || ""}
                  onChange={(e) => updateMaterialInput(materialInput.id, 'upperBound', e.target.value)}
                  disabled={readOnly}
                  placeholder="100"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
      </>
    );
  }
};
