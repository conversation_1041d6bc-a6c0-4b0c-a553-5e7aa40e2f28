/**
 * Default form data for each node in the standard template
 * This pre-populates the connection forms to match the diagram flow
 */

export interface DefaultNodeFormData {
  formData: any;
  outputs: any[];
  technologies: string[];
  technologyFormData: Record<string, any>;
  completedAt: string;
}

// Default form data for Gas Oil Separation (Node 1)
export const gasOilSeparationFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "2", // Connects to Condensation
      outputTechnology: "Boiler",
      energyOutputs: [], // No energy outputs in default template
      matOutputs: [
        {
          id: "material-output-0",
          material: "Raw gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "2", // Connects to Condensation
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Gas Oil Separation",
      startYear: "2000",
      endYear: "2075",
      technologyEmission: "",
      customTechnology: "",
      customActivity: "",
      // Material input: Crude oil from standalone input edge
      materialInputs: [
        {
          id: "material-input-0",
          material: "Crude oil",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "Nil", // Standalone input
          technology: "Nil",
          lowerBound: "",
          upperBound: ""
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Condensation (Node 2)
export const condensationFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "3", // Connects to Amine Absorbing
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Sour gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "3", // Connects to Amine Absorbing
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Condensation",
      startYear: "2000",
      endYear: "2075",
      technologyEmission: "",
      customTechnology: "",
      customActivity: "",
      // Material input: Raw gas from Gas Oil Separation
      materialInputs: [
        {
          id: "material-input-0",
          material: "Raw gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "1", // From Gas Oil Separation
          technology: "Boiler",
          lowerBound: "",
          upperBound: ""
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Amine Absorbing (Node 3)
export const amineAbsorbingFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "4", // Connects to Water Saturation
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Sweet gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "4", // Connects to Water Saturation
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Amine Absorbing",
      startYear: "2000",
      endYear: "2075",
      customTechnology: "",
      customActivity: "",
      // Material input: Sour gas from Condensation
      materialInputs: [
        {
          id: "material-input-0",
          material: "Sour gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "2", // From Condensation
          technology: "Boiler"
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Water Saturation (Node 4)
export const waterSaturationFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "5", // Connects to Dehydration
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Saturated gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "5", // Connects to Dehydration
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Water Saturation",
      startYear: "2000",
      endYear: "2075",
      customTechnology: "",
      customActivity: "",
      // Material input: Sweet gas from Amine Absorbing
      materialInputs: [
        {
          id: "material-input-0",
          material: "Sweet gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "3", // From Amine Absorbing
          technology: "Boiler"
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Glycol Contacting (Node 5)
export const glycolContactingFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "6", // Connects to Mercury Removal
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Dry gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "6", // Connects to Mercury Removal
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Glycol Contacting",
      startYear: "2000",
      endYear: "2075",
      customTechnology: "",
      customActivity: "",
      // Material input: Saturated gas from Water Saturation
      materialInputs: [
        {
          id: "material-input-0",
          material: "Saturated gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "4", // From Water Saturation
          technology: "Boiler"
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Mercury Removal (Node 6)
export const mercuryRemovalFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "7", // Connects to Mercury Gas Condensation
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Purified gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "7", // Connects to Mercury Gas Condensation
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Mercury Removal",
      startYear: "2000",
      endYear: "2075",
      customTechnology: "",
      customActivity: "",
      // Material input: Dry gas from Glycol Contacting
      materialInputs: [
        {
          id: "material-input-0",
          material: "Dry gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "5", // From Glycol Contacting
          technology: "Boiler"
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Mercury Gas Condensation (Node 7)
export const mercuryGasCondensationFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "8", // Connects to Cryogenic Separation
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Processed gas",
          unit: "Tonnes",
          smc: "1",
          final: false,
          connect: "8", // Connects to Cryogenic Separation
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Mercury Gas Condensation",
      startYear: "2000",
      endYear: "2075",
      customTechnology: "",
      customActivity: "",
      // Material input: Purified gas from Mercury Removal
      materialInputs: [
        {
          id: "material-input-0",
          material: "Purified gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "6", // From Mercury Removal
          technology: "Boiler"
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Default form data for Cryogenic Separation (Node 8) - Final processing node
export const cryogenicSeparationFormData: DefaultNodeFormData = {
  formData: {},
  outputs: [
    {
      id: "output-0",
      targetNode: "natural-gas", // Connects to Natural Gas final output
      outputTechnology: "Boiler",
      energyOutputs: [],
      matOutputs: [
        {
          id: "material-output-0",
          material: "Natural gas",
          unit: "Tonnes",
          smc: "1",
          final: true, // This is the final output
          connect: "natural-gas",
          qty: "1",
          qtyUnit: "Tonnes",
          destinationTechnology: "Boiler"
        }
      ]
    }
  ],
  technologies: ["Boiler"],
  technologyFormData: {
    "Boiler": {
      technology: "Boiler",
      activity: "Cryogenic Seperation",
      startYear: "2000",
      endYear: "2075",
      customTechnology: "",
      customActivity: "",
      // Material input: Processed gas from previous steps
      materialInputs: [
        {
          id: "material-input-0",
          material: "Processed gas",
          unit: "Tonnes",
          cost: "1",
          smc: "1",
          sourceActivity: "7", // From previous processing step
          technology: "Boiler"
        }
      ],
      // No energy inputs in default template
      energyInputs: [],
      // No emissions in default template
      emissions: [],
      // Legacy single entries (for compatibility)
      energyInput: { source: "", unit: "GJ", cost: "", sec: "" },
      emission: { source: "", ef: "", unit: "kg" },
      matInput: { material: "", unit: "Tonnes", cost: "", smc: "" },
      // By-products
      byproductTechnology: "Boiler",
      byproductEnergy: { byproduct: "", unit: "GJ", bppo: "", connect: "", replaced: "" },
      byproductMat: { byproduct: "", unit: "Tonnes", bppo: "", connect: "", replaced: "", techEmissionFactor: "", emissionFactor: "", emissionUnit: "" },
      energyByProducts: [],
      materialByProducts: [],
      // Financial
      financial: { capacity: "1", capacityUnit: "Tonnes/day", capitalCostUnit: "1", omCost: "1", operatingAndMaintenanceCost: "1" },
      financialEntries: {}
    }
  },
  completedAt: new Date().toISOString()
};

// Export a mapping of node IDs to their default form data
export const defaultNodeFormDataMap: Record<string, DefaultNodeFormData> = {
  "1": gasOilSeparationFormData,
  "2": condensationFormData,
  "3": amineAbsorbingFormData,
  "4": waterSaturationFormData,
  "5": glycolContactingFormData,
  "6": mercuryRemovalFormData,
  "7": mercuryGasCondensationFormData,
  "8": cryogenicSeparationFormData,
};
